import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowCircleRight2,
  ArrowRight,
  Gallery,
  CloseCircle,
} from 'iconsax-react';
import { Button } from '../../components/button/onboardingButton';
import frame from '../../assets/images/bg-frame.png';
import { toast } from 'react-toastify';
import { AuthServices } from '../../lib/services/auth';
import { Onboarding } from '../prelaunch/onboarding';
import { events } from '../../lib/services/events';
import { useEventStore } from '../../lib/store/event';

interface ImageWithPreview {
  id: string;
  file: File;
  preview: string;
}

interface AddEventImagesProps {
  // onNext?: (images: File[]) => void;
  // onSkip?: () => void;
  initialData?: File[];
  direction?: 'forward' | 'backward';
  eventId?: string;
  eventName?: string;
}

export const AddEventImages = ({
  initialData = [],
  direction = 'forward',
  eventId,
  eventName,
}: AddEventImagesProps) => {
  const [bannerImage, setBannerImage] = useState<File | null>(null);
  const [bannerPreview, setBannerPreview] = useState<string | null>(null);
  const [eventImages, setEventImages] = useState<ImageWithPreview[]>(() => {
    return initialData.map((file, index) => ({
      id: `initial-${index}-${Date.now()}`,
      file,
      preview: URL.createObjectURL(file),
    }));
  });
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  const bannerInputRef = useRef<HTMLInputElement>(null);
  const eventImagesInputRef = useRef<HTMLInputElement>(null);
  const { setCreatedEventData } = useEventStore();

  const MAX_FILE_SIZE_MB = Number(import.meta.env.VITE_MAX_IMAGE_SIZE) || 10;
  const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

  useEffect(() => {
    return () => {
      if (bannerPreview) {
        URL.revokeObjectURL(bannerPreview);
      }
      eventImages.forEach((image) => {
        URL.revokeObjectURL(image.preview);
      });
    };
  }, [bannerPreview, eventImages]);

  const handleBannerImageUpload = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      if (file.size > MAX_FILE_SIZE_BYTES) {
        toast.error(
          `Banner image size must be less than ${MAX_FILE_SIZE_MB}MB`
        );
        return;
      }

      setBannerImage(file);
      const preview = URL.createObjectURL(file);
      setBannerPreview(preview);
    } else if (file && !file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
    }
  };

  const handleEventImagesUpload = (files: FileList) => {
    const newImages: ImageWithPreview[] = [];
    const rejectedFiles: string[] = [];

    Array.from(files).forEach((file) => {
      if (!file.type.startsWith('image/')) {
        rejectedFiles.push(`${file.name} (not an image)`);
        return;
      }

      if (file.size > MAX_FILE_SIZE_BYTES) {
        rejectedFiles.push(
          `${file.name} (exceeds ${MAX_FILE_SIZE_MB}MB limit)`
        );
        return;
      }

      const id = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const preview = URL.createObjectURL(file);
      newImages.push({ id, file, preview });
    });

    if (rejectedFiles.length > 0) {
      toast.error(`Some files were rejected: ${rejectedFiles.join(', ')}`);
    }
    const totalImages = eventImages.length + newImages.length;
    if (totalImages > 4) {
      const allowedCount = 4 - eventImages.length;
      newImages.slice(allowedCount).forEach((image) => {
        URL.revokeObjectURL(image.preview);
      });
      newImages.splice(allowedCount);
      toast.error('Maximum of 4 event images allowed (plus 1 banner image)');
    }

    if (newImages.length > 0) {
      setEventImages((prev) => [...prev, ...newImages]);
    }
  };

  const removeEventImage = (id: string) => {
    setEventImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleEventImagesUpload(files);
    }
  };

  const handleContinue = async () => {
    const eventFiles = eventImages.map((img) => img.file);
    const allImages = bannerImage ? [bannerImage, ...eventFiles] : eventFiles;

    if (allImages.length > 0) {
      if (!eventId) {
        toast.error('Event ID is missing. Cannot upload images.');
        return;
      }

      const oversizedFiles = allImages.filter(
        (file) => file.size > MAX_FILE_SIZE_BYTES
      );
      if (oversizedFiles.length > 0) {
        toast.error(
          `Some images exceed the ${MAX_FILE_SIZE_MB}MB limit. Please remove or replace them.`
        );
        return;
      }

      setIsUploading(true);
      try {
        const response = await AuthServices.uploadFiles(
          allImages,
          'event_image',
          eventId
        );
        console.log('Upload successful:', response);
        if (response && response.data) {
          try {
            const updatedEventResponse = await events.getEventByID(eventId);
            if (updatedEventResponse?.data) {
              setCreatedEventData(updatedEventResponse.data);
            }
          } catch (fetchError) {
            console.error('Failed to fetch updated event data:', fetchError);
          }

          setIsUploading(false);
          setShowOnboarding(true);
        } else {
          throw new Error('Upload response was invalid');
        }
      } catch (error) {
        console.error('Upload failed:', error);
        let errorMessage = 'Failed to upload images. Please try again.';

        if (error && typeof error === 'object') {
          if (
            'response' in error &&
            error.response &&
            typeof error.response === 'object' &&
            'data' in error.response &&
            error.response.data &&
            typeof error.response.data === 'object' &&
            'message' in error.response.data
          ) {
            errorMessage = String(error.response.data.message);
          } else if ('message' in error && typeof error.message === 'string') {
            errorMessage = error.message;
          }
        }

        toast.error(errorMessage);
        setIsUploading(false);
        return;
      }
    } else {
      setShowOnboarding(true);
    }
  };

  const handleSkip = () => {
    setShowOnboarding(true);
  };

  const titleVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  };

  const getCardVariants = (direction: 'forward' | 'backward') => ({
    hidden: {
      opacity: 0,
      x: direction === 'forward' ? 50 : -50,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <motion.h2
          className="text-xl md:text-3xl font-medium text-black"
          initial="hidden"
          animate="visible"
          variants={titleVariants}>
          <AnimatePresence mode="wait">
            <motion.span
              key="upload-photos"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}>
              Upload <span className="text-primary-650 italic">photos</span> of
              your
              <br />
              event 📷
            </motion.span>
          </AnimatePresence>
        </motion.h2>

        <button
          type="button"
          onClick={handleSkip}
          className="border border-cus-orange-150 cursor-pointer text-cus-orange-500 flex items-center gap-2 px-3 py-2 rounded-full text-sm font-semibold disabled:opacity-50 disabled:cursor-not-allowed">
          <span>Skip</span>
          <div className="bg-cus-orange-100/30 h-4 w-4 rounded-full flex justify-center items-center">
            <ArrowRight color="#FF6630" size="10" />
          </div>
        </button>
      </div>

      <motion.div
        className="bg-white rounded-[20px] mt-14 px-5 py-6 w-full"
        initial="hidden"
        animate="visible"
        variants={getCardVariants(direction)}>
        <div className="mb-14 ">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              ref={bannerInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleBannerImageUpload(file);
              }}
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
            />
            {bannerPreview ? (
              <img
                src={bannerPreview}
                alt="Banner"
                className="absolute inset-0 w-full h-full object-cover"
              />
            ) : (
              <Gallery variant="Bulk" size="62" color="#992600" />
            )}
          </div>
          <p
            className=" text-xs italic text-primary-750 -mt-15 font-medium cursor-pointer"
            onClick={() => bannerInputRef.current?.click()}>
            Upload banner image
          </p>
        </div>
        {eventImages.length > 0 && (
          <div className="mb-6">
            <div className="flex flex-wrap gap-3">
              {eventImages.map((image, index) => (
                <div key={image.id} className="relative">
                  <img
                    src={image.preview}
                    alt={`Event ${index + 1}`}
                    className="w-21 h-25 object-cover rounded-lg border border-grey-200"
                  />
                  <button
                    onClick={() => removeEventImage(image.id)}
                    className="absolute -top-2 -right-1 bg-white rounded-full">
                    <CloseCircle size="20" color="#CC0000" variant="Bold" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {eventImages.length < 4 && (
          <div className="mb-6">
            <div
              className={`rounded-2xl relative overflow-hidden cursor-pointer transition-all ${
                isDragOver ? 'ring-2 ring-primary-650 ring-opacity-50' : ''
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => eventImagesInputRef.current?.click()}>
              <div className="w-full h-auto">
                <img
                  src={frame}
                  alt="frame"
                  className="w-full h-auto object-contain"
                />
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-white px-4 py-2 rounded-full ">
                  <p className="text-primary text-sm font-bold italic">
                    Click to Upload Images of your Events
                  </p>
                </div>
              </div>
              <input
                ref={eventImagesInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => {
                  const files = e.target.files;
                  if (files) handleEventImagesUpload(files);
                }}
                className="hidden"
              />
            </div>
            <div className=" flex justify-between items-center mt-2">
              <p className="text-[#6B7280] text-xs">
                Allowed formats: PNG, JPEG, JPG up to {MAX_FILE_SIZE_MB}MB each
              </p>
              <p className="text-[#343CD8] text-xs italic">
                Maximum of 4 selections
              </p>
            </div>
          </div>
        )}

        <Button
          variant="primary"
          size="md"
          className={`text-white  ${
            eventImages.length > 0 && bannerImage
              ? 'bg-primary-650'
              : '!bg-primary-650/35 mt-6'
          } ${eventImages.length === 4 && 'mt-40'}`}
          onClick={handleContinue}
          disabled={isUploading}
          iconRight={
            <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
          }>
          {isUploading ? 'Uploading...' : 'Continue'}
        </Button>
      </motion.div>
      {showOnboarding && <Onboarding eventName={eventName} />}
    </div>
  );
};
