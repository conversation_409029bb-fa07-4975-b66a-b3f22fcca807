import {
  ArrowCircleLeft2,
  // ArrowCircleRight2,
  CloseCircle,
} from "iconsax-react";
import { useEffect } from "react";

interface GiftItem {
  id: number;
  image: string;
  name: string;
  price?: string;
  quantity?: number;
  description?: string;
  item_link?: string;
  mostWanted?: boolean;
}

interface PreviewGiftItemsProps {
  items: GiftItem[];
  onBack: () => void;
  onDeleteItem?: (id: number) => void;
  // onNextStep?: () => void;
}

export const PreviewGiftItems = ({
  items,
  onBack,
  onDeleteItem,
}: // onNextStep,
PreviewGiftItemsProps) => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  return (
    <div className="px-4 md:px-0 w-full max-w-[560px] h-full relative font-rethink mb-40">
      {/* {onNextStep && (
        <div className="mt-8 flex justify-end absolute top-0 right-[-160px]">
          <button
            onClick={onNextStep}
            className="bg-primary-650 text-white py-2.5 px-4 rounded-full cursor-pointer flex items-center gap-2"
          >
            Preview
            <div className="bg-white/30 rounded-full p-0.5">
              <ArrowCircleRight2 size={16} color="#fff" variant="Bold" />
            </div>
          </button>
        </div>
      )} */}
      <div className="flex items-center mb-4">
        <button
          onClick={onBack}
          className="flex items-center cursor-pointer gap-2 italic text-primary-750 font-medium"
        >
          <ArrowCircleLeft2 color="#5F66F3" variant="Bulk" size={20} />
          Back
        </button>
      </div>

      <div className="space-y-4">
        {items.map((item) => (
          <div
            key={item.id}
            className="flex items-center rounded-2xl p-2.5 shadow-[0px_12px_120px_0px_#5F5F5F0F] border border-grey-150 relative"
          >
            {/* {item.mostWanted && (
              <div className="absolute top-1 right-1 bg-primary-150 text-primary-750 text-xs font-bold px-1.5 py-0.5 rounded-full flex items-center z-10 italic">
                📍 MOST WANTED
              </div>
            )} */}
            <img
              src={item.image || "/api/placeholder/48/48"}
              alt={item.name}
              className="object-contain w-10 h-10 rounded-full mr-4"
            />
            <div className="flex items-start justify-between w-full">
              <div>
                <h3 className="font-semibold text-sm text-dark-blue-200">
                  {item.name}
                </h3>
                <p className="text-grey-650 text-xs">
                  N {item.price || 0}{" "}
                  {item?.item_link && (
                    <>
                      •{" "}
                      <a
                        href={`https://${item.item_link.replace(
                          /^https?:\/\//,
                          ""
                        )}`}
                        target="_blank"
                        rel="noopener noreferrer nofollow"
                        className="italic underline text-cus-orange-100 hover:text-cus-orange-200"
                      >
                        Link to Item
                      </a>
                    </>
                  )}
                </p>
              </div>
              <button
                onClick={() => onDeleteItem && onDeleteItem(item.id)}
                className="cursor-pointer hover:opacity-70 transition-opacity"
              >
                <CloseCircle color="#9499F7" variant="Bulk" size={20} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
